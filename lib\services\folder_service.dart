import 'package:note_x/lib.dart';

class FolderService {
  final FolderApiService _apiService;

  FolderService({
    FolderApiService? apiService,
  }) : _apiService = apiService ?? FolderApiServiceImpl();

  // Create root folder
  Future<FolderModel> createFolder({required String folderName}) async {
    try {
      // Call API first
      final folder = await _apiService.createFolder(folderName);
      
      // Update Hive on success
      await HiveFolderService.createFolder(
        name: folder.folderName,
        folderBackendId: folder.backendId,
      );
      
      return folder;
    } catch (e) {
      throw Exception('Failed to create folder: $e');
    }
  }

  // Create subfolder
  Future<FolderModel> createSubfolder({
    required String name,
    required String parentFolderId,
  }) async {
    try {
      // Validate parent exists
      final parentFolder = HiveFolderService.getFolderById(folderBackendId: parentFolderId);
      if (parentFolder == null) {
        throw Exception('Parent folder not found');
      }
      
      if (parentFolder.level >= FolderModel.maxLevel) {
        throw Exception('Cannot create subfolder: Maximum level (${FolderModel.maxLevel}) reached');
      }

      // Call API first
      final subfolder = await _apiService.createSubFolder(name, parentFolderId);
      
      // Update Hive on success
      await HiveFolderService.createFolder(
        name: subfolder.folderName,
        folderBackendId: subfolder.backendId,
        parentFolderId: parentFolderId,
      );
      
      return subfolder;
    } catch (e) {
      throw Exception('Failed to create subfolder: $e');
    }
  }

  // Edit folder
  Future<FolderModel> editFolder({
    required String folderId,
    required String newName,
  }) async {
    try {
      // Call API first
      final updatedFolder = await _apiService.editFolder(folderId, newName);
      
      // Update Hive on success
      await HiveFolderService.renameFolderById(
        folderLocalId: folderId,
        newName: newName,
      );
      
      return updatedFolder;
    } catch (e) {
      throw Exception('Failed to edit folder: $e');
    }
  }

  // Delete folder
  Future<bool> deleteFolder({
    required String folderId,
    bool deleteNotes = false,
  }) async {
    try {
      // Call API first
      final success = await _apiService.deleteFolder(folderId: folderId);
      
      if (success) {
        // Update Hive on success
        await HiveFolderService.deleteFolder(
          folderLocalId: folderId,
          deleteNotes: deleteNotes,
        );
      }
      
      return success;
    } catch (e) {
      throw Exception('Failed to delete folder: $e');
    }
  }

  // Move folder
  Future<void> moveFolder({
    required String folderId,
    required String targetFolderId,
  }) async {
    try {
      // Call API first
      await _apiService.moveFolderToFolder(folderId, targetFolderId);
      
      // Update Hive on success
      await HiveFolderService.moveFolderToFolder(
        folderId: folderId,
        targetFolderId: targetFolderId,
      );
      
    } catch (e) {
      throw Exception('Failed to move folder: $e');
    }
  }

  // Sync all folders from API
  Future<List<FolderModel>> syncAllFolders() async {
    try {
      // Get all folders from API
      final apiFolders = await _apiService.getAllFolders();
      
      // Save to Hive with proper hierarchy
      await HiveFolderService.saveAllFolders(folders: apiFolders);
      
      return apiFolders;
    } catch (e) {
      throw Exception('Failed to sync folders: $e');
    }
  }

  // Bulk operations
  Future<void> bulkDeleteItems({
    required List<String> folderIds,
    required List<String> noteIds,
    required bool deleteNotes,
  }) async {
    try {
      // Call API first
      await _apiService.bulkDeleteItems(
        folderIds: folderIds,
        noteIds: noteIds,
        deleteNotes: deleteNotes,
      );
      
      // Update Hive on success
      for (final folderId in folderIds) {
        await HiveFolderService.deleteFolder(
          folderLocalId: folderId,
          deleteNotes: deleteNotes,
        );
      }
      
      // Delete notes directly
      final noteBox = HiveService().noteBox;
      for (final noteId in noteIds) {
        await noteBox.delete(noteId);
      }
      
    } catch (e) {
      throw Exception('Failed to bulk delete: $e');
    }
  }

  Future<void> bulkMoveNotesToFolder({
    required String targetFolderId,
    required List<String> folderIds,
    required List<String> noteIds,
  }) async {
    try {
      // Call API first
      await _apiService.bulkMoveNotesToFolder(
        targetFolderId: targetFolderId,
        folderIds: folderIds,
        noteIds: noteIds,
      );
      
      // Update Hive on success
      for (final folderId in folderIds) {
        await HiveFolderService.moveFolderToFolder(
          folderId: folderId,
          targetFolderId: targetFolderId,
        );
      }
      
      // Move notes
      final targetFolder = HiveFolderService.getFolderById(folderBackendId: targetFolderId);
      if (targetFolder != null) {
        final noteBox = HiveService().noteBox;
        for (final noteId in noteIds) {
          final note = noteBox.get(noteId);
          if (note != null) {
            await HiveFolderService.addNoteToFolder(
              note: note,
              folderBackendId: targetFolderId,
            );
          }
        }
      }
      
    } catch (e) {
      throw Exception('Failed to bulk move: $e');
    }
  }

  // Note operations
  Future<void> addNoteToFolder({
    required NoteModel note,
    required String folderBackendId,
  }) async {
    await HiveFolderService.addNoteToFolder(
      note: note,
      folderBackendId: folderBackendId,
    );
  }

  // Query methods - delegate to HiveFolderService
  List<FolderModel> getAllFolders() => HiveFolderService.getAllFolders();
  
  List<FolderModel> getRootFolders() => HiveFolderService.getRootFolders();
  
  List<FolderModel> getSubfolders({required String folderBackendId}) =>
      HiveFolderService.getSubFolders(folderBackendId: folderBackendId);
  
  FolderModel? getFolderById({required String folderBackendId}) =>
      HiveFolderService.getFolderById(folderBackendId: folderBackendId);
  
  String getFolderNameById(String folderBackendId) =>
      HiveFolderService.getNameFolderByBackEndId(folderBackendId);
  
  Future<List<NoteModel>> getNotesInFolder({required String folderBackendId}) =>
      HiveFolderService.getNotesInFolder(folderBackendId: folderBackendId);
  
  Future<List<NoteModel>> getNotesFailed({required String userId}) =>
      HiveFolderService.getNotesFailed(userId: userId);

  // Advanced query methods
  List<FolderModel> searchFolders(String query) =>
      HiveFolderService.searchFolders(query);
  
  List<FolderModel> getFoldersAtLevel(int level) =>
      HiveFolderService.getFoldersAtLevel(level);
  
  List<FolderModel> getFolderTree() => HiveFolderService.getFolderTree();
  
  Map<String, dynamic> getFolderStatistics() =>
      HiveFolderService.getFolderStatistics();
  
  String printFolderTree() => HiveFolderService.printFolderTree();

  // Maintenance methods
  Future<void> fixDataConsistency() => HiveFolderService.fixDataConsistency();
}
