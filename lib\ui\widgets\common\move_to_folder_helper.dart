import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:note_x/lib.dart';

class MoveToFolderHelper {
  static void showBottomSheet(
    BuildContext context, {
    List<String>? foldersToBeMovedIds,
    String? jumpToFolderId,
    List<NoteModel>? notesToBeMoved,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) {
        return FutureBuilder(
          future: HiveFolderService.fixSubFolderIds(),
          builder: (context, snapshot) {
            debugPrint('FutureBuilder state: ${snapshot.connectionState}');
            if (snapshot.hasError) {
              debugPrint('Error in FutureBuilder: ${snapshot.error}');
            }
            return ValueListenableBuilder(
              valueListenable: HiveService().folderBox.listenable(),
              builder: (context, folderBox, child) {
                debugPrint('FolderBox length: ${folderBox.length}');
                final allFolders = HiveFolderService.getAllFolders();
                debugPrint('All folders count: ${allFolders.length}');
                debugPrint('All folders: ${allFolders.map((f) => f.folderName).join(', ')}');

                // Get root folders (folders without parents)
                final rootFolders = allFolders.where((folder) {
                  // A folder is a root folder if its parentFolderId is empty or null
                  // or if its parent folder doesn't exist in the box
                  if (folder.parentFolderId == null || folder.parentFolderId!.isEmpty) {
                    return true;
                  }
                  final parent = HiveService().folderBox.get(folder.parentFolderId!);
                  return parent == null;
                }).toList()
                  ..sort((a, b) => MyUtils.convertToTimestamp(b.createdAt)
                      .compareTo(MyUtils.convertToTimestamp(a.createdAt)));
                debugPrint('Root folders count: ${rootFolders.length}');
                debugPrint('Root folders: ${rootFolders.map((f) => f.folderName).join(', ')}');

                FolderModel? selectedFolder;

                // Auto-select the target folder if jumpToFolderId is provided
                if (jumpToFolderId != null && jumpToFolderId.isNotEmpty) {
                  selectedFolder = HiveFolderService.getFolderById(folderBackendId: jumpToFolderId);
                }

                return StatefulBuilder(
                  builder: (context, setState) {
                    return SafeArea(
                      child: Container(
                        padding: EdgeInsets.only(
                          left: context.isTablet ? 16 : 16.w,
                          right: context.isTablet ? 16 : 16.w,
                          top: context.isTablet ? 16 : 16.h,
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        height: MediaQuery.of(context).size.height * 0.95,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Header
                            Row(
                              children: [
                                Expanded(
                                  child: CommonText(
                                    S.current.add_folder,
                                    style: TextStyle(
                                      fontSize: context.isTablet ? 22 : 20.sp,
                                      fontWeight: FontWeight.w600,
                                      color: context.colorScheme.mainPrimary,
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                    onTap: () => Navigator.pop(context),
                                    child: SvgPicture.asset(
                                      Assets.icons.icCloseWhite,
                                      width: context.isTablet ? 32 : 24.w,
                                      height: context.isTablet ? 32 : 24.w,
                                      fit: BoxFit.contain,
                                      colorFilter: ColorFilter.mode(
                                        context.colorScheme.mainPrimary,
                                        BlendMode.srcIn,
                                      ),
                                    ))
                              ],
                            ),
                            Expanded(
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  ...rootFolders.map((folder) => FolderTile(
                                        folder: folder,
                                        isSelected: selectedFolder?.backendId == folder.backendId,
                                        onTap: (f) {
                                          setState(() => selectedFolder = f);
                                        },
                                        jumpToFolderId: jumpToFolderId,
                                        selectedFolder: selectedFolder,
                                      )),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 16.h),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: AppCommonButton(
                                      backgroundColor: context.colorScheme.secondary,
                                      onPressed: selectedFolder == null
                                          ? null
                                          : () {
                                              Navigator.pop(
                                                  context); // Close bottom sheet first
                                              final controller =
                                                  TextEditingController();
                                              showCreateFolderDialog(
                                                context,
                                                controller: controller,
                                                onPressed: () async {
                                                  final folderService = GetIt
                                                      .instance
                                                      .get<FolderService>();
                                                  await folderService
                                                      .createSubfolder(
                                                    name: controller.text.trim(),
                                                    parentFolderId: selectedFolder!
                                                        .backendId,
                                                  );
                                                  await HiveFolderService.fixSubFolderIds();
                                                  Navigator.pop(context);
                                                },
                                                onClosed: () {
                                                  controller.dispose();
                                                },
                                                title: S.current.create_new_folder,
                                                contentButton: S.current.create,
                                                hintText: S.current.required,
                                                initialValue: '',
                                              );
                                            },
                                      textWidget: CommonText(
                                        S.current.create,
                                        style: TextStyle(
                                          fontSize: context.isTablet ? 18 : 16.sp,
                                            color: context.colorScheme.mainPrimary,
                                            fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW10,
                                  Expanded(
                                    child: AppCommonButton(
                                      backgroundColor: context.colorScheme.mainBlue,
                                      onPressed: selectedFolder == null
                                          ? null
                                          : () async {
                                              final folderCubit =
                                                  GetIt.instance.get<FolderDetailCubit>();
                                              await folderCubit.moveFolderToFolder(
                                                foldersToBeMovedIds ?? [],
                                                notesToBeMoved ?? [],
                                                selectedFolder!.backendId,
                                              );
                                              // Fix folder hierarchy after moving folders
                                              await HiveFolderService.fixSubFolderIds();
                                              Navigator.pop(context);
                                            },
                                      textWidget: CommonText(
                                        S.current.move,
                                        style: TextStyle(
                                          fontSize: context.isTablet ? 18 : 16.sp,
                                          color: context.colorScheme.mainPrimary,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }
}

class FolderTile extends StatefulWidget {
  final FolderModel folder;
  final bool isSelected;
  final Function(FolderModel folder)? onTap;
  final int level;
  final String? jumpToFolderId;
  final FolderModel? selectedFolder; // Add this to track selected folder for subfolders

  const FolderTile({
    Key? key,
    required this.folder,
    this.isSelected = false,
    this.onTap,
    this.level = 0,
    this.jumpToFolderId,
    this.selectedFolder,
  }) : super(key: key);

  @override
  State<FolderTile> createState() => _FolderTileState();
}

class _FolderTileState extends State<FolderTile> {
  late bool expanded;

  @override
  void initState() {
    super.initState();
    expanded = _shouldExpand();
  }

  /// Check if this folder should be expanded based on jumpToFolderId
  bool _shouldExpand() {
    if (widget.jumpToFolderId == null || widget.jumpToFolderId!.isEmpty) {
      return false;
    }

    // If this folder is the target folder, expand it
    if (widget.folder.backendId == widget.jumpToFolderId) {
      return true;
    }

    // If this folder contains the target folder in its hierarchy, expand it
    return _containsTargetFolder(widget.folder.backendId, widget.jumpToFolderId!);
  }

  /// Check if a folder contains the target folder in its hierarchy
  bool _containsTargetFolder(String folderBackendId, String targetFolderId) {
    final targetFolder = HiveFolderService.getFolderById(folderBackendId: targetFolderId);
    if (targetFolder == null) return false;

    // Check if the target folder is a descendant of this folder
    String? currentParentId = targetFolder.parentFolderId;
    while (currentParentId != null && currentParentId.isNotEmpty) {
      if (currentParentId == folderBackendId) {
        return true;
      }
      final parentFolder = HiveFolderService.getFolderById(folderBackendId: currentParentId);
      currentParentId = parentFolder?.parentFolderId;
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    final subFolders = HiveFolderService.getSubFolders(
        folderBackendId: widget.folder.backendId);

    return Column(
      children: [
        ListTile(
          contentPadding: EdgeInsets.only(left: 16.w * widget.level),
          tileColor: widget.isSelected ? Colors.blue.withOpacity(0.1) : null,
          leading: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (subFolders.isNotEmpty)
                GestureDetector(
                  onTap: () => setState(() => expanded = !expanded),
                  child: SvgPicture.asset(
                    expanded
                        ? Assets.icons.icExpandMore
                        : Assets.icons.icExpandLess,
                    width: context.isTablet ? 24 : 16.w,
                    height: context.isTablet ? 24 : 16.w,
                    fit: BoxFit.contain,
                  ),
                ),
              SvgPicture.asset(
                Assets.icons.icFlipFolderMini,
                width: context.isTablet ? 48 : 32.w,
                height: context.isTablet ? 48 : 32.w,
                fit: BoxFit.contain,
              ),
            ],
          ),
          title: Text(widget.folder.folderName),
          onTap: () {
            if (widget.onTap != null) {
              widget.onTap!(widget.folder);
            }
            if (subFolders.isNotEmpty) {
              setState(() => expanded = !expanded);
            }
          },
        ),
        if (expanded)
          ...subFolders.map((sub) => FolderTile(
                folder: sub,
                isSelected: widget.selectedFolder?.backendId == sub.backendId,
                onTap: widget.onTap,
                level: widget.level + 1,
                jumpToFolderId: widget.jumpToFolderId,
                selectedFolder: widget.selectedFolder,
              )),
      ],
    );
  }
}
