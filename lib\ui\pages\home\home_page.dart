import 'dart:io';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:custom_refresh_indicator/custom_refresh_indicator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/base/app/app_state.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/home/<USER>/home_state.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:note_x/ui/pages/home/<USER>/animated_bottom_nav_bar.dart';

class HomePage extends StatefulWidget {
  static const routeName = 'home';

  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends BasePageStateDelegate<HomePage, HomeCubit>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    _initializePage();
  }

  void _initializePage() {
    cubit.initTabBars(this);
    cubit.initSharing(context);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.handleWidgetApp(context);
    });

    _setupInitialData();

    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) {
        cubit.checkPendingShareAfterLogin();
        cubit.handleWidgetAppInit();
      }
    });

    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.home_show);
    AnalyticsService.logEventScreenView(
      screenClass: EventScreenClass.homePage,
      screenName: EventScreenName.home_page,
    );
  }

  void _setupInitialData() {
    cubit.syncAllNotes(true);
    cubit.getCommunityNote();
    cubit.initAllNotesDataListener();
    cubit.initListnerOnMultiSelectNote();
  }

  @override
  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<HomeCubit, HomeState>(
      listenWhen: (previous, current) =>
          previous.isSyncing != current.isSyncing,
      listener: (context, state) {
        Future.delayed(const Duration(milliseconds: 300), () {
          showDialog();
        });
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Container(
      color: context.colorScheme.mainBackground,
      child: SafeArea(
        bottom: Platform.isIOS ? false : true,
        child: Padding(
          padding: EdgeInsets.only(top: context.isTablet ? 10 : 0.h),
          child: Scaffold(
            backgroundColor: context.colorScheme.mainBackground,
            resizeToAvoidBottomInset: false,
            appBar: _buildAppBar(),
            extendBody: true,
            bottomNavigationBar: AnimatedBottomNavBar(
              key: cubit.bottomNavKey,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              onAnimationComplete: () {},
              child: _BottomNavigationBar(cubit: cubit),
            ),
            body: PageView(
              controller: cubit.pageController,
              children: [
                _HomeView(cubit: cubit),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void showDialog() {
    if (cubit.appCubit.isUserFree()) {
      if (!MyUtils.isShownToday()) {
        showDialogSaleOffIap(context).then((value) async {
          if (value) {
            if (mounted) {
              Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) => const PurchasePage(
                    from: PurchasePageFrom.iapDialogSaleOff,
                    isUiSpecial: true,
                  ),
                  fullscreenDialog: true,
                ),
              );
            }
          } else {
            if (await MyUtils.isNotificationPermanentlyDeniedOrAccepted()) {
              MyUtils.requestReview();
            } else {
              g.get<FirebaseNotificationService>().requestPermission();
            }
          }
        });
      }
    } else {
      g.get<FirebaseNotificationService>().requestPermission();
      Future.delayed(const Duration(milliseconds: 300), () {
        MyUtils.requestReview();
      });
    }
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: context.colorScheme.mainBackground,
      leadingWidth: double.infinity,
      toolbarHeight: context.isTablet ? 80 : 60.h,
      leading: _AppBarLeading(cubit: cubit),
      actions: [
        _SearchButton(cubit: cubit),
      ],
    );
  }
}

// Separated Widgets
class _AppBarLeading extends StatelessWidget {
  final HomeCubit cubit;

  const _AppBarLeading({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppCubit, AppState>(
      buildWhen: (previous, current) =>
          previous.appUser.userType != current.appUser.userType,
      builder: (context, state) => CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () => _navigateToSettings(context),
        child: _buildUserInfo(context),
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 0, 10.w, 0),
      child: Row(
        children: [
          _UserAvatar(cubit: cubit, user: user),
          _UserInfo(cubit: cubit, user: user),
        ],
      ),
    );
  }

  void _navigateToSettings(BuildContext context) {
    cubit.onSettingsTap();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingPage()),
    );
  }
}

class _UserAvatar extends StatelessWidget {
  final HomeCubit cubit;
  final User? user;

  const _UserAvatar({required this.cubit, this.user});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppCubit, AppState>(
      buildWhen: (previous, current) =>
          previous.appUser.userType != current.appUser.userType,
      builder: (context, state) {
        final isPro = state.appUser.userType == UserType.pro ||
            state.appUser.userType == UserType.proLite;

        return Container(
          padding: EdgeInsets.only(top: context.isTablet ? 12 : 12.h),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Container(
                width: context.isTablet ? 46 : 46.w,
                height: context.isTablet ? 46 : 46.w,
                margin: EdgeInsets.only(right: context.isTablet ? 8 : 8.w),
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primaryBlue,
                      AppColors.primaryBlue,
                    ],
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.all(1.w),
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: context.colorScheme.mainBackground,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(100),
                      child: Padding(
                        padding: EdgeInsets.all(context.isTablet ? 3 : 3.w),
                        child: SvgPicture.asset(
                          Assets.icons.icDefaultAvatar,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              if (isPro)
                Positioned(
                  top: context.isTablet ? -11 : -11.w,
                  right: context.isTablet ? 7 : 7.w,
                  child: SvgPicture.asset(
                    Assets.icons.icCrown,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

class _UserInfo extends StatelessWidget {
  final HomeCubit cubit;
  final User? user;

  const _UserInfo({required this.cubit, this.user});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CommonText(
            '${cubit.getGreeting()}✌',
            style: TextStyle(
                fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w600,
                color: context.colorScheme.mainPrimary,
                height: 1.5),
            maxLines: 1,
          ),
          SizedBox(
            height: cubit.appCubit.isTablet ? 20 : 16.h,
            child: ClipRect(
              child: IgnorePointer(
                child: AnimatedTextKit(
                  animatedTexts: cubit
                      .getContentGreeting()
                      .map(
                        (text) => FadeAnimatedText(
                          text,
                          textStyle: TextStyle(
                              fontSize: cubit.appCubit.isTablet ? 14 : 12.sp,
                              fontWeight: FontWeight.w400,
                              color: context.colorScheme.mainGray,
                              height: 1.3),
                          duration: const Duration(milliseconds: 3000),
                          textAlign: TextAlign.start,
                        ),
                      )
                      .toList(),
                  repeatForever: true,
                  pause: const Duration(milliseconds: 0),
                  displayFullTextOnTap: true,
                  stopPauseOnTap: true,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SearchButton extends StatelessWidget {
  final HomeCubit cubit;

  const _SearchButton({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () => _navigateToSearch(context),
      child: Padding(
        padding: EdgeInsets.only(top: 12.h, right: 16.w),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(context.isTablet ? 100 : 100.r),
          child: Container(
            width: context.isTablet ? 40 : 40.w,
            height: context.isTablet ? 40 : 40.h,
            decoration: BoxDecoration(
              color: context.colorScheme.mainNeutral,
            ),
            child: Padding(
              padding: EdgeInsets.all(context.isTablet ? 8 : 8.w),
              child: SvgPicture.asset(
                cubit.appCubit.isReverseView
                    ? Assets.icons.icFlipSearchEnable
                    : Assets.icons.icSearchEnable,
                width: 24.w,
                height: 24.h,
                colorFilter: ColorFilter.mode(
                  context.colorScheme.mainPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToSearch(BuildContext context) {
    cubit.onSearchTap();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SearchPage(),
        fullscreenDialog: true,
      ),
    );
  }
}

class _HomeView extends StatelessWidget {
  final HomeCubit cubit;

  const _HomeView({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _TabBarSection(cubit: cubit),
        _TabBarViewSection(cubit: cubit),
      ],
    );
  }
}

class _TabBarSection extends StatelessWidget {
  final HomeCubit cubit;

  const _TabBarSection({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: cubit.appCubit.isTablet
          ? const EdgeInsets.fromLTRB(36, 12, 36, 16)
          : EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 10.h),
      child: Container(
        height: context.isLandscape ? 32 * 1.4 : 32.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100.r),
          color: context.colorScheme.mainNeutral,
        ),
        child: BlocBuilder<HomeCubit, HomeState>(
            buildWhen: (previous, current) =>
                previous.currentSelectedTabBarIndex !=
                current.currentSelectedTabBarIndex,
            builder: (context, state) {
              return TabBar(
                controller: cubit.tabBarController,
                isScrollable: false,
                onTap: cubit.onTabBarTap,
                tabs: [
                  _buildAllNotesTab(context),
                  _buildFoldersTab(context),
                  _buildSharedTab(context),
                ],
                indicator: BoxDecoration(
                  color: context.colorScheme.mainBlue,
                  borderRadius: BorderRadius.circular(100.r),
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                labelPadding: EdgeInsets.symmetric(horizontal: 10.w),
                labelStyle: _labelStyle(context, true),
                unselectedLabelStyle: _labelStyle(context, false),
                dividerColor: Colors.transparent,
                indicatorWeight: 0,
              );
            }),
      ),
    );
  }

  Widget _buildAllNotesTab(BuildContext context) {
    return Tab(
      child: InkWell(
        onTap: () {
          cubit.onAllNotesTabTap();
          if (cubit.tabBarController.index == 0) {
            buildFilterAndSortBottomSheet(context, cubit);
          } else {
            cubit.tabBarController.animateTo(0);
          }
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: BlocBuilder<HomeCubit, HomeState>(
                buildWhen: (previous, current) =>
                    previous.selectedFilterIndex !=
                        current.selectedFilterIndex ||
                    previous.currentSelectedTabBarIndex !=
                        current.currentSelectedTabBarIndex,
                builder: (context, state) {
                  return CommonText(
                    _getFilterDisplayText(state.selectedFilterIndex),
                    style: TextStyle(
                        fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                        fontWeight: FontWeight.w500,
                        color: cubit.tabBarController.index == 0
                            ? Colors.white
                            : context.colorScheme.mainPrimary,
                        overflow: TextOverflow.ellipsis,
                        height: 1),
                    maxLines: 1,
                  );
                },
              ),
            ),
            SizedBox(width: 4.w),
            SvgPicture.asset(
              Assets.icons.icArrowUp2,
              colorFilter: cubit.tabBarController.index == 0
                  ? null
                  : ColorFilter.mode(
                      context.colorScheme.mainPrimary,
                      BlendMode.srcIn,
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// Returns the display text for the filter based on the selected index
  String _getFilterDisplayText(int filterIndex) {
    final List<String> filterItems = [
      S.current.all_note,
      S.current.record,
      S.current.audio,
      S.current.youtube,
      S.current.web_link,
      S.current.document_tab,
    ];

    return filterItems[filterIndex];
  }

  Widget _buildFoldersTab(BuildContext context) {
    return Tab(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          CommonText(
            S.current.folder,
            style: TextStyle(
              height: 1,
              fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w500,
              color: cubit.tabBarController.index == 1
                  ? Colors.white
                  : context.colorScheme.mainPrimary,
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 1,
          ),
          ValueListenableBuilder(
            valueListenable: HiveService().noteFailedBox.listenable(),
            builder: (context, box, child) {
              final isShowRedDot = box.values
                  .toList()
                  .where((note) =>
                      note.userId == GetIt.I<AppCubit>().getAppUser().id)
                  .toList()
                  .isNotEmpty;
              return isShowRedDot
                  ? Positioned(
                      right: -22.w,
                      top: 1.h,
                      child: Container(
                        width: 6.w,
                        height: 6.w,
                        decoration: const BoxDecoration(
                          color: AppColors.primaryRed,
                          shape: BoxShape.circle,
                        ),
                      ),
                    )
                  : const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSharedTab(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: CommonText(
        S.current.shared,
        style: TextStyle(
          height: 1,
          fontWeight: FontWeight.w500,
          fontSize: cubit.appCubit.isTablet ? 17 : 14.sp,
          color: cubit.tabBarController.index == 2
              ? Colors.white
              : context.colorScheme.mainPrimary,
          overflow: TextOverflow.ellipsis,
        ),
        maxLines: 1,
      ),
    );
  }

  TextStyle _labelStyle(BuildContext context, bool isSelected) => TextStyle(
        fontSize: cubit.appCubit.isTablet ? 17 : 13.sp,
        fontWeight: FontWeight.w700,
        color: isSelected ? context.colorScheme.mainPrimary : Colors.white,
        fontFamily: AppConstants.fontSFPro,
      );
}

class _TabBarViewSection extends StatelessWidget {
  final HomeCubit cubit;

  const _TabBarViewSection({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: TabBarView(
        controller: cubit.tabBarController,
        children: [
          _AllNotesView(cubit: cubit),
          _FolderView(cubit: cubit),
          _CommunityNotesView(cubit: cubit),
        ],
      ),
    );
  }
}

class _AllNotesView extends StatelessWidget {
  final HomeCubit cubit;

  const _AllNotesView({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return CustomMaterialIndicator(
      onRefresh: () async {
        HapticFeedback.mediumImpact();
        await cubit.onRefreshNote();
      },
      indicatorBuilder: (_, __) => const CupertinoActivityIndicator(),
      child: BlocBuilder<HomeCubit, HomeState>(
        buildWhen: (previous, current) =>
            previous.filteredNotes != current.filteredNotes ||
            previous.isSyncing != current.isSyncing,
        builder: (context, state) => state.isSyncing
            ? const Center(child: CupertinoActivityIndicator(radius: 16))
            : HomeItemNoteListView(
                key: const PageStorageKey("all_notes"),
                listNote: state.filteredNotes,
                isFilter: state.selectedFilterIndex != 0 ||
                    state.selectedSortIndex != 0,
                onDeleteNote: cubit.deleteNote,
                onNoteItemTap: cubit.onNoteItemTap,
                controller: cubit.multiSelectController,
              ),
      ),
    );
  }
}

class _FolderView extends StatelessWidget {
  final HomeCubit cubit;

  const _FolderView({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return CustomMaterialIndicator(
      backgroundColor: context.colorScheme.mainNeutral,
      onRefresh: () async {
        HapticFeedback.mediumImpact();
        cubit.onRefreshFolder();
      },
      indicatorBuilder: (_, __) => const Padding(
        padding: EdgeInsets.all(6.0),
        child: CupertinoActivityIndicator(radius: 16),
      ),
      child: ValueListenableBuilder(
        valueListenable: HiveService().noteFailedBox.listenable(),
        builder: (context, box, _) => FolderPage(
          key: const PageStorageKey("failed_notes"),
          unsyncedNotes: box.values
              .where((note) =>
                  note.userId == GetIt.I<AppCubit>().getAppUser().id &&
                  note.backendNoteId.isEmpty)
              .toList(),
        ),
      ),
    );
  }
}

class _CommunityNotesView extends StatelessWidget {
  final HomeCubit cubit;

  const _CommunityNotesView({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        if (state.homePageCommunityNoteViewState !=
            HomePageCommunityNoteViewState.getCommunityNoteSuccess) {
          return const Center(
            child: CupertinoActivityIndicator(radius: 16),
          );
        }
        return CommunityNoteListView(
          key: const PageStorageKey("community_notes"),
          listNote: cubit.getCommunityNotes,
          onNoteItemTap: cubit.onCommunityNoteItemTap,
        );
      },
    );
  }
}

class _BottomNavigationBar extends StatelessWidget {
  final HomeCubit cubit;

  const _BottomNavigationBar({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _GradientOverlay(),
        _NavigationItems(cubit: cubit),
      ],
    );
  }
}

class _GradientOverlay extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 97.h,
      decoration: BoxDecoration(
          gradient: LinearGradient(
        begin: Alignment.bottomCenter,
        end: Alignment.topCenter,
        colors: [
          context.isDarkMode
              ? context.colorScheme.mainNeutral.withOpacity(0.7)
              : context.colorScheme.mainNeutral.withOpacity(0.3),
          Colors.transparent,
        ],
      )),
    );
  }
}

class _NavigationItems extends StatefulWidget {
  final HomeCubit cubit;

  const _NavigationItems({required this.cubit});

  @override
  State<_NavigationItems> createState() => _NavigationItemsState();
}

class _NavigationItemsState extends State<_NavigationItems>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  OverlayEntry? _overlayEntry;
  OverlayEntry? _backgroundEntry;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _backgroundEntry?.remove();
    _backgroundEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final bottomNavWidth = screenWidth - (context.isTablet ? 140 * 2 : 31 * 2);
    final contentWidth = bottomNavWidth - (context.isTablet ? 40 : 40.w);
    final navigationButtonWidth = context.isTablet ? 32 : 24.w;
    final bottomTabHorizontalPadding = (contentWidth -
            (context.isTablet ? 72 : 72.w) -
            (navigationButtonWidth * 4)) /
        10;

    return Positioned(
      top: 0,
      left: context.isTablet ? 140 : 31,
      right: context.isTablet ? 140 : 31,
      bottom: Platform.isIOS ? 24.h : 24.h,
      child: Center(
        child: Container(
            height: widget.cubit.appCubit.isTablet ? 75 : 72.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(52.r),
              color: context.colorScheme.mainNeutral,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: context.isTablet ? 20 : 20.w),
              child: Row(
                children: [
                  _NavigationButton(
                    icon: Assets.icons.icHomeYt,
                    title: S.current.link,
                    padding: EdgeInsets.symmetric(
                        horizontal: bottomTabHorizontalPadding),
                    onTap: () => _navigateToYoutube(context),
                  ),
                  _NavigationButton(
                    icon: Assets.icons.icHomeAudio,
                    title: S.current.audio,
                    padding: EdgeInsets.symmetric(
                        horizontal: bottomTabHorizontalPadding),
                    onTap: () => _navigateToAudio(context),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: bottomTabHorizontalPadding),
                    child: _RecordButton(cubit: widget.cubit),
                  ),
                  _NavigationButton(
                    icon: Assets.icons.icHomeDoc,
                    title: S.current.doc,
                    padding: EdgeInsets.symmetric(
                        horizontal: bottomTabHorizontalPadding),
                    onTap: () => _navigateToDocument(context),
                  ),
                  _NavigationButton(
                    icon: Assets.icons.icGridOthers,
                    title: S.current.others,
                    padding: EdgeInsets.symmetric(
                        horizontal: bottomTabHorizontalPadding),
                    onTap: () => _showOtherOptionsMenu(),
                  ),
                ],
              ),
            )),
      ),
    );
  }

  void _navigateToYoutube(BuildContext context) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
          builder: (context) => YoutubePage(
            content: widget.cubit.getSharedContent,
            onClose: _resetSharedData,
            onCreateNoteSuccess: () => widget.cubit.safeNavigateToPage(0),
          ),
        ))
        .then((_) => _resetControllers());
  }

  void _navigateToAudio(BuildContext context) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
          builder: (context) => UploadAudioPage(
            sharedMediaFile: widget.cubit.getSharedMedia,
            onClose: _resetSharedData,
            onCreateNoteSuccess: () => widget.cubit.safeNavigateToPage(0),
          ),
        ))
        .then((_) => _resetControllers());
  }

  void _navigateToDocument(BuildContext context) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
          builder: (context) => UploadDocumentPage(
            sharedMediaFile: widget.cubit.getSharedMedia,
            onClose: _resetSharedData,
            onCreateNoteSuccess: () => widget.cubit.safeNavigateToPage(0),
          ),
        ))
        .then((_) => _resetControllers());
  }

  void _navigateToText(BuildContext context) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
          builder: (context) => UploadTextPage(
            onClose: _resetSharedData,
            onCreateNoteSuccess: () => widget.cubit.safeNavigateToPage(0),
          ),
        ))
        .then((_) => _resetControllers());
  }

  void _navigateToImage(BuildContext context) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
          builder: (context) => UploadImagePage(
            onClose: _resetSharedData,
            onCreateNoteSuccess: () => widget.cubit.safeNavigateToPage(0),
          ),
        ))
        .then((_) => _resetControllers());
  }

  void _navigateToCamera(BuildContext context) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
          builder: (context) => CameraPage(
            onClose: _resetSharedData,
            onCreateNoteSuccess: () => widget.cubit.safeNavigateToPage(0),
          ),
        ))
        .then((_) => _resetControllers());
  }

  void _showOtherOptionsMenu() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_other_clicked,
    );
    _removeOverlay();

    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    final buttonPosition = button.localToGlobal(Offset.zero, ancestor: overlay);
    final sideMargin = context.isTablet ? 140.0 : 31.w;
    final menuWidth = context.isTablet ? 200.0 : 200.w;
    final leftPosition =
        MediaQuery.of(context).size.width - sideMargin - menuWidth;

    final double menuOffset = context.isTablet ? 120 : 145.h;
    final double menuTop = buttonPosition.dy - menuOffset;

    // Tạo overlay entry
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: leftPosition,
        top: menuTop,
        child: Material(
          color: Colors.transparent,
          child: FadeTransition(
            opacity: _animationController,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.3),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: _animationController,
                curve: Curves.easeOut,
              )),
              child: Container(
                padding: EdgeInsets.zero,
                width: menuWidth,
                decoration: BoxDecoration(
                  color: context.colorScheme.mainNeutral,
                  borderRadius: BorderRadius.circular(16.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildMenuItemForOverlay(
                      title: S.current.camera,
                      iconAsset: Assets.icons.icCamera,
                      onTapAction: () {
                        _removeOverlay();
                        _navigateToCamera(context);
                      },
                    ),
                    _buildMenuItemForOverlay(
                      title: S.current.upload_image,
                      iconAsset: Assets.icons.icImage,
                      onTapAction: () {
                        _removeOverlay();
                        _navigateToImage(context);
                      },
                    ),
                    _buildMenuItemForOverlay(
                      title: S.current.text,
                      iconAsset: Assets.icons.icHomeText,
                      onTapAction: () {
                        _removeOverlay();
                        _navigateToText(context);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    _backgroundEntry = OverlayEntry(
      builder: (context) => Positioned.fill(
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            _animationController.reverse().then((_) {
              _removeOverlay();
            });
          },
          child: Container(
            color: Colors.transparent,
          ),
        ),
      ),
    );

    // Thêm overlay vào context
    Overlay.of(context).insert(_backgroundEntry!);
    Overlay.of(context).insert(_overlayEntry!);

    // Bắt đầu animation
    _animationController.forward();
  }

  Widget _buildMenuItemForOverlay({
    required String title,
    required String iconAsset,
    required Function() onTapAction,
  }) {
    return GestureDetector(
      onTap: onTapAction,
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: context.isTablet ? 44 : 44.h,
        padding: widget.cubit.appCubit.isTablet
            ? const EdgeInsets.symmetric(horizontal: 16)
            : EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          children: [
            Expanded(
              child: CommonText(
                title,
                style: TextStyle(
                  fontSize: widget.cubit.appCubit.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w500,
                  color: context.colorScheme.mainPrimary,
                ),
              ),
            ),
            SizedBox(width: 10.w),
            SvgPicture.asset(
              iconAsset,
              width: context.isTablet ? 24 : 24.w,
              height: context.isTablet ? 24 : 24.w,
              colorFilter: ColorFilter.mode(
                context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _resetSharedData() {
    widget.cubit.setSharedNoteType(null);
    widget.cubit.setSharedContent(null);
    widget.cubit.setSharedMedia(null);
  }

  void _resetControllers() {
    if (widget.cubit.pageController.page!.round() != 0) {
      widget.cubit.pageController.jumpToPage(0);
    }
    if (widget.cubit.tabBarController.index != 0) {
      widget.cubit.tabBarController.animateTo(0);
    }
  }
}

class _NavigationButton extends StatelessWidget {
  final String icon;
  final EdgeInsets padding;
  final String title;
  final VoidCallback onTap;

  const _NavigationButton({
    required this.icon,
    required this.padding,
    required this.onTap,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    final double totalWidth =
        (context.isTablet ? 32 : 24.w) + (padding.horizontal);

    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: padding,
            child: SvgPicture.asset(
              icon,
              width: context.isTablet ? 32 : 24.w,
              height: context.isTablet ? 32 : 24.w,
              colorFilter: ColorFilter.mode(
                context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
          AppConstants.kSpacingItem4,
          SizedBox(
            width: totalWidth,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: CommonText(
                title,
                style: TextStyle(
                  fontSize: context.isTablet ? 12 : 10.sp,
                  fontWeight: FontWeight.w500,
                  color: context.colorScheme.mainGray.withOpacity(0.6),
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
              ),
            ),
          )
        ],
      ),
    );
  }
}

class _RecordButton extends StatelessWidget {
  final HomeCubit cubit;

  const _RecordButton({required this.cubit});

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () => _navigateToRecord(context),
      child: Container(
        width: cubit.appCubit.isTablet ? 72 : 72.w,
        height: cubit.appCubit.isTablet ? 72 : 72.h,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              Assets.icons.icHomeRecord.path,
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Center(
          child: Lottie.asset(
            Assets.videos.recordHome,
            width: cubit.appCubit.isTablet ? 72 : 72.w,
            height: cubit.appCubit.isTablet ? 72 : 72.h,
          ),
        ),
      ),
    );
  }

  void _navigateToRecord(BuildContext context) {
    cubit.onBottomBarNewNoteRecordTabTap();
    Navigator.of(context)
        .push(CupertinoPageRoute(
          builder: (context) => const RecordAppPage(isAutoStart: true),
        ))
        .then((_) => _resetControllers());
  }

  void _resetControllers() {
    if (cubit.pageController.page!.round() != 0) {
      cubit.pageController.jumpToPage(0);
    }
    if (cubit.tabBarController.index != 0) {
      cubit.tabBarController.animateTo(0);
    }
  }
}
