import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/folder/widget/selection_bottom_bar.dart';

class MultiSelectController extends ChangeNotifier {
  bool _isMultiSelectMode = false;
  Set<String> _selectedNoteIds = {};

  bool get isMultiSelectMode => _isMultiSelectMode;
  Set<String> get selectedNoteIds => _selectedNoteIds;
  int get selectedCount => _selectedNoteIds.length;

  void enterMultiSelectMode(String noteId) {
    if (!_isMultiSelectMode) {
      _isMultiSelectMode = true;
      _selectedNoteIds.add(noteId);
      notifyListeners();
    }
  }

  void exitMultiSelectMode() {
    _isMultiSelectMode = false;
    _selectedNoteIds.clear();
    notifyListeners();
  }

  void toggleNoteSelection(String noteId) {
    if (_selectedNoteIds.contains(noteId)) {
      _selectedNoteIds.remove(noteId);
    } else {
      _selectedNoteIds.add(noteId);
    }
    notifyListeners();
  }

  void selectAll(List<String> noteIds) {
    _selectedNoteIds = noteIds.toSet();
    notifyListeners();
  }

  void deselectAll() {
    _selectedNoteIds.clear();
    notifyListeners();
  }

  bool isSelected(String noteId) {
    return _selectedNoteIds.contains(noteId);
  }
}

class HomeItemNoteListView extends StatefulWidget {
  final List<NoteModel> listNote;
  final Function(String)? onDeleteNote;
  final Function()? onNoteItemTap;
  final bool isFilter;
  final Function(List<String>)? onSelectedNotesChanged;
  final MultiSelectController? controller;

  const HomeItemNoteListView({
    super.key,
    required this.listNote,
    this.onDeleteNote,
    this.onNoteItemTap,
    this.isFilter = false,
    this.onSelectedNotesChanged,
    this.controller,
  });

  @override
  State<HomeItemNoteListView> createState() => _HomeItemNoteListViewState();
}

class _HomeItemNoteListViewState extends State<HomeItemNoteListView>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final appCubit = GetIt.instance.get<AppCubit>();
  final homeCubit = GetIt.instance.get<HomeCubit>();
  final _localService = GetIt.instance.get<LocalService>();

  late List<FolderModel> listFolder;

  // Multi-select controller
  late MultiSelectController _multiSelectController;

  // Animation controller for smooth transitions
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    listFolder = [
      FolderModel(
        id: MyNoteDetailCubit.allNotesId,
        folderName: S.current.all_note,
        backendId: '',
      ),
      ...HiveFolderService.getAllFolders()
    ];

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Initialize multi-select controller
    _multiSelectController = widget.controller ?? MultiSelectController();

    // Set initial animation state
    _animationController.value = 0.0;

    // Listen to multi-select changes for animation
    _multiSelectController.addListener(_onMultiSelectChanged);

    // Add scroll listener
    _scrollController.addListener(() {
      if (HomeOverlayManager().hasActiveOverlays &&
          _scrollController.position.userScrollDirection !=
              ScrollDirection.idle) {
        HomeOverlayManager().closeAllOverlays();
      }
    });
  }

  void _onMultiSelectChanged() {
    if (_multiSelectController.isMultiSelectMode) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    // Notify parent of selection changes
    widget.onSelectedNotesChanged
        ?.call(_multiSelectController.selectedNoteIds.toList());
  }

  @override
  void dispose() {
    _multiSelectController.removeListener(_onMultiSelectChanged);
    if (widget.controller == null) {
      _multiSelectController.dispose();
    }
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  // Multi-select functions
  void selectAll() {
    _multiSelectController
        .selectAll(widget.listNote.map((note) => note.id).toList());
  }

  void deselectAll() {
    _multiSelectController.deselectAll();
  }

  void _toggleNoteSelection(String noteId) {
    _multiSelectController.toggleNoteSelection(noteId);
  }

  void _enterMultiSelectMode(String noteId) {
    _multiSelectController.enterMultiSelectMode(noteId);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.listNote.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListenableBuilder(
      listenable: _multiSelectController,
      builder: (context, _) {
        return Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: _multiSelectController.isMultiSelectMode ? 48 : 0,
              child: _multiSelectController.isMultiSelectMode
                  ? _buildSelectDeselectWidget(context)
                  : const SizedBox.shrink(),
            ),
            // Note list
            Expanded(child: _buildNoteListView(context)),
            SelectionBottomBar(
              isTablet: context.isTablet,
              onDeletePressed: () {
                final noteIdList = _multiSelectController.selectedNoteIds;
                //TODO congnm
                // MoveToFolderHelper.showBottomSheet(context, sourceFolderId: sourceFolderId)
              },
            )
          ],
        );
      },
    );
  }

  Widget _buildSelectDeselectWidget(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Select All / Deselect All button (far left)
        TextButton(
          onPressed: () {
            if (_multiSelectController.selectedCount ==
                widget.listNote.length) {
              // If all notes are selected, deselect all
              deselectAll();
            } else {
              // If not all notes are selected, select all
              selectAll();
            }
          },
          child: Text(
            _multiSelectController.selectedCount == widget.listNote.length
                ? 'Deselect All' // You might want to use S.current.deselect_all if you have localization
                : 'Select All', // You might want to use S.current.select_all if you have localization
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: context.colorScheme.mainBlue,
            ),
          ),
        ),

        // Selected count indicator (center)
        Text(
          '${_multiSelectController.selectedCount} selected',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray,
          ),
        ),

        // Cancel button (far right)
        TextButton(
          onPressed: () {
            _multiSelectController.exitMultiSelectMode();
          },
          child: Text(
            'Cancel', // You might want to use S.current.cancel if you have localization
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: context.colorScheme.mainGray,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    if (widget.isFilter) return _EmptyFilterNote();
    return const EmptyNotePortrait();
  }

  Widget _buildNoteListView(BuildContext context) {
    // Determine if we need to show the purchase banner
    final bool showPurchaseBanner = appCubit.isUserFree() &&
        _multiSelectController.isMultiSelectMode == false;

    // Calculate the number of special items (banner) to show
    final int specialItemsCount = showPurchaseBanner ? 1 : 0;

    // Calculate the total item count
    final int totalItemCount = widget.listNote.length + specialItemsCount;

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).padding.bottom,
        left: context.isTablet ? 24 : 0,
        right: context.isTablet ? 24 : 0,
      ),
      itemCount: totalItemCount,
      itemBuilder: (context, index) {
        // Handle purchase banner at index 0
        if (index == 0 && showPurchaseBanner) {
          return const PurchaseBannerSliderWidget();
        }

        // Calculate the actual note index in the list
        final int noteIndex = index - specialItemsCount;

        // Build and return the note item
        return _buildNoteItem(context, widget.listNote[noteIndex], noteIndex);
      },
    );
  }

  Widget _buildNoteItem(BuildContext context, NoteModel note, int index) {
    // Get the item's position in the list view
    final listViewOffset = _scrollController.offset;

    // Calculate item height and position
    final double itemHeight = context.isTablet ? 80.0 : 72.0;
    final double itemPadding = context.isTablet ? 16.0 : 12.0;
    final double totalItemHeight = itemHeight + itemPadding;

    // Calculate the position of this item in the viewport
    final double itemPosition = index * totalItemHeight;
    final double itemPositionInViewport = itemPosition - listViewOffset;

    // Calculate the visible viewport height (excluding app bar, status bar, tab bar, bottom nav)
    final double viewportHeight = MediaQuery.of(context).size.height -
        kToolbarHeight - // App bar height
        MediaQuery.of(context).padding.top - // Status bar
        (context.isTablet ? 80.0 : 60.0) - // Tab bar height
        (context.isTablet ? 75.0 : 72.0) - // Bottom navigation height
        MediaQuery.of(context).padding.bottom; // Bottom safe area

    // Determine if the item is in the bottom half of the visible area
    final bool isInBottomHalf = itemPositionInViewport > (viewportHeight / 2);

    return FadeAnimation(
      (1.0 + index) / 4,
      Padding(
        padding: context.isTablet
            ? const EdgeInsets.symmetric(horizontal: 10, vertical: 8)
            : EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
        child: ListenableBuilder(
          listenable: _multiSelectController,
          builder: (context, child) {
            final bool isSelected = _multiSelectController.isSelected(note.id);
            final bool isMultiSelectMode =
                _multiSelectController.isMultiSelectMode;

            return HomeItemNoteWidget(
              noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
              isMultiSelectMode: isMultiSelectMode,
              isSelected: isSelected,
              onTap: () {
                widget.onNoteItemTap?.call();
                _navigateToNoteDetail(context, note);
              },
              onLongPress: () {
                if (!isMultiSelectMode) {
                  _enterMultiSelectMode(note.id);
                }
              },
              onSelectionTap: () => _toggleNoteSelection(note.id),
              overflowMenu: HomeOverFlowMenu(
                  note: note,
                  isCommunityNote: false,
                  onShowFolder: (context) {
                    MoveToFolderHelper.showBottomSheet(
                      context,
                      notesToBeMoved: [note],
                      foldersToBeMovedIds: [note.folderId],
                    );
                  },
                  onShowBottomSheet: (exportType) {
                    ExportNoteHelper.showBottomSheet(context, exportType, note);
                  },
                  focusNode: FocusNode(),
                  onShowSharingView: Func0(() {
                    NoteSharingHelper.showBottomSheet(context, note);
                  }),
                  onDeleteNote: () {
                    widget.onDeleteNote?.call(note.id);
                  },
                  isInBottomHalf: isInBottomHalf,
                  calculateIsInBottomHalf: () {
                    final currentListViewOffset = _scrollController.offset;
                    final currentItemPosition = index * totalItemHeight;
                    final currentPositionInViewport =
                        currentItemPosition - currentListViewOffset;
                    final itemBottomPosition =
                        currentPositionInViewport + totalItemHeight;
                    return itemBottomPosition > (viewportHeight * 0.7);
                  }),
            );
          },
        ),
      ),
    );
  }

  void _navigateToNoteDetail(BuildContext context, NoteModel note) async {
    // Get saved tabs before navigation
    final savedTabs = await _initTabBar();

    if (!mounted) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MyNoteDetailPage(
          isCommunityNote: false,
          // Regular notes are not community notes
          noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
          isTablet: context.isTablet,
          from: NoteDetailPageFrom.homeScreen,
          savedTabs: savedTabs,
        ),
      ),
    );
  }

  Future<List<TabType>> _initTabBar() async {
    return await _localService.loadSelectedItems();
  }
}

class _EmptyFilterNote extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                Assets.icons.icEmptyNoteNotexEmpty,
                width: 140,
                height: 140,
              ),
              AppConstants.kSpacingItem12,
              CommonText(
                S.current.no_notes_found,
                style: TextStyle(
                  fontSize: context.isTablet ? 18 : 16.sp,
                  fontWeight: FontWeight.w400,
                  color: context.colorScheme.mainGray,
                  fontFamily: AppConstants.fontSFPro,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
