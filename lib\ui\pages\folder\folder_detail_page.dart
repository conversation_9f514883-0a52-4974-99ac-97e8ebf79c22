import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:uuid/uuid.dart';

import '../../../base/base_page_state.dart';

class FolderDetailPage extends StatefulWidget {
  const FolderDetailPage({
    super.key,
    required this.folder,
    this.isFolderFailed = false,
  });

  final FolderModel folder;
  final bool isFolderFailed;

  @override
  State<FolderDetailPage> createState() => _FolderDetailPageState();
}

class _FolderDetailPageState
    extends BasePageStateDelegate<FolderDetailPage, FolderDetailCubit> {
  TextEditingController editFolderController = TextEditingController();
  final TextEditingController _createFolderController = TextEditingController();

  final ValueNotifier<bool> _selectionModeNotifier = ValueNotifier<bool>(false);
  final ValueNotifier<List<String>> _selectedItemsNotifier =
      ValueNotifier<List<String>>([]);
  List<NoteModel> _currentNotes = [];

  Future<List<NoteModel>>? _notesFuture;

  @override
  void initState() {
    super.initState();
    cubit.init(widget.folder);
    _notesFuture = widget.isFolderFailed
        ? HiveFolderService.getNotesFailed(userId: cubit.userId)
        : HiveFolderService.getNotesInFolder(
            folderBackendId: widget.folder.backendId);
    _loadNotes();
  }

  @override
  Widget buildPage(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: _selectionModeNotifier,
      builder: (context, isSelectionMode, _) {
        return ValueListenableBuilder<List<String>>(
          valueListenable: _selectedItemsNotifier,
          builder: (context, selectedItems, __) {
            return Scaffold(
                appBar: isSelectionMode
                    ? _buildSelectionAppBar(selectedItems)
                    : AppBarWidget(
                        title: widget.folder.folderName,
                        onPressed: () {
                          cubit.onFolderDetailBack();
                          Navigator.pop(context);
                        },
                        actions: [
                          widget.isFolderFailed
                              ? const SizedBox()
                              : Row(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(right: 8.w),
                                      child: GestureDetector(
                                        onTap: _showCreateFolderDialog,
                                        child: SvgPicture.asset(
                                          Assets.icons.icCreateSubFolder,
                                          width: 24.w,
                                          height: 24.w,
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(right: 16.w),
                                      child: FolderMoreOptions(
                                        cubit: cubit,
                                        folder: widget.folder,
                                        editFolderController:
                                            editFolderController,
                                        icon: Assets.icons.icMore,
                                        isInDetailPage: true,
                                        onEnableSelectionMode:
                                            _toggleSelectionMode,
                                      ),
                                    ),
                                  ],
                                ),
                        ],
                      ),
                body: _buildPageBody(isSelectionMode));
          },
        );
      },
    );
  }

  Future<void> _loadNotes() async {
    try {
      final notes = await _notesFuture!;
      _currentNotes = notes;
    } catch (e) {
      _currentNotes = [];
    }
  }

  @override
  void dispose() {
    _selectionModeNotifier.dispose();
    _selectedItemsNotifier.dispose();
    _createFolderController.dispose();
    super.dispose();
  }

  void _toggleSelectionMode() {
    final newValue = !_selectionModeNotifier.value;
    _selectionModeNotifier.value = newValue;
    if (!newValue) {
      _selectedItemsNotifier.value = [];
    }
  }

  void _toggleItemSelection(String itemId) {
    final currentItems = List<String>.from(_selectedItemsNotifier.value);
    if (currentItems.contains(itemId)) {
      currentItems.remove(itemId);
    } else {
      currentItems.add(itemId);
    }
    _selectedItemsNotifier.value = currentItems;
  }

  void _selectAll(List<String> allItemIds) {
    _selectedItemsNotifier.value = List<String>.from(allItemIds);
  }

  void _deselectAll() {
    _selectedItemsNotifier.value = [];
  }

  void _showCreateFolderDialog() {
    cubit.onCreateFolderTap();
    showCreateFolderDialog(
      context,
      onPressed: () async {
        final folderName = _createFolderController.text.trim();
        if (folderName.isEmpty) return;

        // Create subfolder locally using Hive
        final newFolderId = const Uuid().v4();
        // await HiveFolderService.createSubFolder(
        //   folderBackendId: newFolderId,
        //   parentFolderId: widget.folder.backendId,
        // );
        //TODO: check logic congnm

        _createFolderController.text = '';

        // Refresh the notes list after creating new folder
        _notesFuture = widget.isFolderFailed
            ? HiveFolderService.getNotesFailed(userId: cubit.userId)
            : HiveFolderService.getNotesInFolder(
                folderBackendId: widget.folder.backendId);
      },
      onClosed: () {
        cubit.onCreateFolderCancel();
      },
      title: S.current.create_new_folder,
      contentButton: S.current.create,
      hintText: S.current.required,
      controller: _createFolderController,
      initialValue: '',
    );
  }

  PreferredSizeWidget _buildSelectionAppBar(List<String> selectedItems) {
    final totalItems = _getTotalItemCount();
    final allSelected = selectedItems.length == totalItems && totalItems > 0;
    return AppBar(
      centerTitle: true,
      leadingWidth: cubit.appCubit.isTablet ? 100 : 100.w,
      leading: GestureDetector(
        onTap: allSelected ? _deselectAll : () => _selectAll(_getAllItemIds()),
        child: Container(
          padding: EdgeInsets.only(left: cubit.appCubit.isTablet ? 16 : 16.w),
          alignment: Alignment.centerLeft,
          child: CommonText(
            allSelected ? S.current.deselect : S.current.select_all,
            style: TextStyle(
              fontSize: cubit.appCubit.isTablet ? 14 : 12.sp,
              fontWeight: FontWeight.w500,
              color: context.colorScheme.mainBlue,
            ),
          ),
        ),
      ),
      title: CommonText(
        '${selectedItems.length} ${S.current.items}',
        style: TextStyle(
          fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
          fontWeight: FontWeight.w600,
          color: context.colorScheme.mainPrimary,
        ),
      ),
      actions: [
        Padding(
          padding: EdgeInsets.only(right: cubit.appCubit.isTablet ? 16 : 16.w),
          child: GestureDetector(
            onTap: _toggleSelectionMode,
            child: SvgPicture.asset(
              Assets.icons.icCloseWhite,
              width: context.isTablet ? 32 : 24.w,
              height: context.isTablet ? 32 : 24.w,
              fit: BoxFit.contain,
              colorFilter: ColorFilter.mode(
                context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPageBody(bool isSelectionMode) {
    return Column(
      children: [
        Expanded(
          child: ValueListenableBuilder<Box<NoteModel>>(
            valueListenable: widget.isFolderFailed
                ? HiveService().noteFailedBox.listenable()
                : HiveService().noteBox.listenable(),
            builder: (context, noteBox, child) {
              return FutureBuilder<List<NoteModel>>(
                future: _notesFuture,
                builder: (context, snapshot) {
                  if (_notesFuture == null) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.connectionState ==
                      ConnectionState.waiting) {
                    return Center(
                      child: Lottie.asset(
                        Assets.videos.commonLoading,
                        width: 50.w,
                        height: 50.h,
                      ),
                    );
                  } else if (snapshot.hasData) {
                    if (snapshot.data == null || snapshot.data!.isEmpty) {
                      _currentNotes = [];
                      return Center(
                        child: Column(
                          children: [
                            AppConstants.kSpacingItem48,
                            SvgPicture.asset(
                              Assets.icons.icEmptyNoteNotexEmpty,
                              width: 160.w,
                              height: 160.h,
                            ),
                            AppConstants.kSpacingItem12,
                            CommonText(
                              S.current.no_notes_in_folder,
                              style: TextStyle(
                                fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                                fontWeight: FontWeight.w600,
                                color: context.colorScheme.mainPrimary,
                              ),
                            ),
                            AppConstants.kSpacingItem20,
                            AppCommonButton(
                                width: cubit.appCubit.isTablet ? 200 : 160.w,
                                height: cubit.appCubit.isTablet ? 36 : 36.h,
                                borderRadius: BorderRadius.circular(24.r),
                                backgroundColor: context.colorScheme.mainBlue,
                                textWidget: CommonText(
                                  S.current.add_note,
                                  style: TextStyle(
                                    fontSize:
                                        cubit.appCubit.isTablet ? 16 : 14.sp,
                                    fontWeight: FontWeight.w500,
                                    color: context.colorScheme.mainPrimary,
                                  ),
                                ),
                                onPressed: () {
                                  // cubit.onAddNoteToFolderTap(
                                  //   folderBackendId: widget.folder.backendId,
                                  //   folderName: widget.folder.folderName,
                                  // );
                                })
                          ],
                        ),
                      );
                    } else {
                      final notes = snapshot.data!;
                      _currentNotes = notes;

                      return ListView.builder(
                        itemCount: notes.length,
                        itemBuilder: (context, index) {
                          final note = notes[index];
                          return _buildNoteItem(note, isSelectionMode,
                              key: ValueKey(note.id));
                        },
                      );
                    }
                  } else {
                    _currentNotes = [];
                    return const Center(child: CommonText('No notes found'));
                  }
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNoteItem(NoteModel note, bool isSelectionMode, {Key? key}) {
    return ValueListenableBuilder<List<String>>(
      valueListenable: _selectedItemsNotifier,
      builder: (context, selectedItems, _) {
        final itemId = 'note_${note.id}';
        final isSelected = selectedItems.contains(itemId);
        return Row(
          children: [
            if (isSelectionMode)
              Padding(
                padding: EdgeInsets.only(left: 8.w, right: 4.w),
                child: GestureDetector(
                  onTap: () => _toggleItemSelection(itemId),
                  child: SvgPicture.asset(
                    isSelected
                        ? Assets.icons.icCheckOb
                        : Assets.icons.icUncheckOb,
                    width: 24.w,
                    height: 24.w,
                  ),
                ),
              ),
            Expanded(
              child: Padding(
                padding: context.isTablet
                    ? const EdgeInsets.symmetric(horizontal: 10, vertical: 8)
                    : EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
                child: HomeItemNoteWidget(
                  noteModel: note,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => MyNoteDetailPage(
                          isCommunityNote: false,
                          noteModel: note.backendNoteId.isNotEmpty
                              ? note
                              : note.copyWith(),
                          isTablet: context.isTablet,
                          from: NoteDetailPageFrom.folderDetailScreen,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
      key: key,
    );
  }

  List<String> _getAllItemIds() {
    List<String> ids = [];
    ids.addAll(_currentNotes.map((note) => 'note_${note.id}'));

    return ids;
  }

  int _getTotalItemCount() {
    return _currentNotes.length;
  }
}
